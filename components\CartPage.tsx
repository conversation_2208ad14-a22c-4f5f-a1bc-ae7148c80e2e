import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCart } from '../contexts/CartContext';
import { useAuth } from '../contexts/AuthContext';
import { TrashIcon, PlusIcon, MinusIcon, ShoppingBagIcon } from './common/Icon';

const CartPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { cartItems, cartSummary, loading, removeFromCart, updateQuantity } = useCart();
  const [visibleItems, setVisibleItems] = useState<Set<number>>(new Set());
  const [processingItems, setProcessingItems] = useState<Set<string>>(new Set());

  // Redirect if not logged in
  useEffect(() => {
    if (!user && !loading) {
      navigate('/login');
    }
  }, [user, loading, navigate]);

  // Animate items on load
  useEffect(() => {
    const timer = setTimeout(() => {
      cartItems.forEach((_, index) => {
        setTimeout(() => {
          setVisibleItems(prev => new Set([...prev, index]));
        }, index * 100);
      });
    }, 200);

    return () => clearTimeout(timer);
  }, [cartItems]);

  const handleQuantityChange = async (cartItemId: string, newQuantity: number) => {
    setProcessingItems(prev => new Set([...prev, cartItemId]));
    await updateQuantity(cartItemId, newQuantity);
    setProcessingItems(prev => {
      const newSet = new Set(prev);
      newSet.delete(cartItemId);
      return newSet;
    });
  };

  const handleRemoveItem = async (cartItemId: string) => {
    setProcessingItems(prev => new Set([...prev, cartItemId]));
    await removeFromCart(cartItemId);
    setProcessingItems(prev => {
      const newSet = new Set(prev);
      newSet.delete(cartItemId);
      return newSet;
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-page-bg-light to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-accent-teal mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your cart...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen font-sans relative overflow-hidden">
      {/* Enhanced Background with Patterns and Glass Morphism */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-page-bg-light to-gray-100">
        {/* Animated Background Patterns */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-brand-accent-teal/15 to-transparent rounded-full blur-3xl animate-parallax-float"></div>
          <div className="absolute bottom-0 left-0 w-80 h-80 bg-gradient-to-tr from-brand-main-red/10 to-transparent rounded-full blur-3xl animate-parallax-float animation-delay-300"></div>
        </div>

        {/* Subtle Grid Pattern */}
        <div className="absolute inset-0 opacity-5" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgba(9, 184, 166, 0.3) 1px, transparent 0)`,
          backgroundSize: '40px 40px'
        }}></div>
      </div>

      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="text-center mb-12 scroll-animate opacity-100 transform-none">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Shopping Cart
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Review your selected items and proceed to checkout
          </p>
        </div>

        {cartItems.length === 0 ? (
          // Empty Cart State
          <div className="text-center py-16">
            <div className="bg-white/90 backdrop-blur-sm rounded-3xl shadow-xl border border-white/50 p-12 max-w-md mx-auto">
              <div className="w-24 h-24 bg-gradient-to-br from-brand-accent-teal/20 to-brand-main-red/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <ShoppingBagIcon size={40} className="text-gray-400" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Your cart is empty</h3>
              <p className="text-gray-600 mb-8">
                Discover our amazing products and add them to your cart
              </p>
              <button
                onClick={() => navigate('/store')}
                className="bg-gradient-to-r from-brand-accent-teal to-brand-accent-teal-darker text-white px-8 py-3 rounded-full font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300"
              >
                Continue Shopping
              </button>
            </div>
          </div>
        ) : (
          // Cart Items
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Cart Items List */}
            <div className="lg:col-span-2 space-y-6">
              {cartItems.map((item, index) => {
                const isVisible = visibleItems.has(index);
                const isProcessing = processingItems.has(item.id);
                const product = item.product;

                if (!product) return null;

                return (
                  <div
                    key={item.id}
                    className={`bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-white/50 p-6 transition-all duration-700 ${
                      isVisible ? 'opacity-100 transform-none' : 'opacity-0 translate-y-8'
                    } ${isProcessing ? 'opacity-50' : ''}`}
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <div className="flex flex-col sm:flex-row gap-6">
                      {/* Product Image */}
                      <div className="flex-shrink-0">
                        <div className="w-32 h-32 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl overflow-hidden">
                          <img
                            src={product.image_url}
                            alt={product.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      </div>

                      {/* Product Details */}
                      <div className="flex-grow">
                        <div className="flex justify-between items-start mb-4">
                          <div>
                            <h3 className="text-xl font-bold text-gray-900 mb-2">{product.name}</h3>
                            <p className="text-gray-600 text-sm mb-2">{product.category}</p>
                            {/* Show discount info if available */}
                            {product.original_price && product.discount ? (
                              <div className="flex flex-col">
                                <div className="flex items-center space-x-2 mb-1">
                                  <span className="bg-brand-main-red text-white text-xs px-2 py-1 rounded-full font-bold">
                                    {product.discount}
                                  </span>
                                  <span className="text-sm text-gray-500 line-through">
                                    AED {product.original_price.toFixed(2)}
                                  </span>
                                </div>
                                <p className="text-brand-accent-teal font-semibold text-lg">
                                  AED {product.price.toFixed(2)}
                                </p>
                                <span className="text-xs text-brand-accent-teal font-medium">
                                  Save AED {(product.original_price - product.price).toFixed(2)} each
                                </span>
                              </div>
                            ) : (
                              <p className="text-brand-accent-teal font-semibold text-lg">
                                AED {product.price.toFixed(2)}
                              </p>
                            )}
                          </div>
                          <button
                            onClick={() => handleRemoveItem(item.id)}
                            disabled={isProcessing}
                            className="p-2 text-red-500 hover:bg-red-50 rounded-full transition-colors duration-200 disabled:opacity-50"
                            aria-label="Remove item"
                          >
                            <TrashIcon size={20} />
                          </button>
                        </div>

                        {/* Quantity Controls */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <span className="text-gray-600 font-medium">Quantity:</span>
                            <div className="flex items-center space-x-2">
                              <button
                                onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                                disabled={isProcessing || item.quantity <= 1}
                                className="w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                              >
                                <MinusIcon size={16} />
                              </button>
                              <span className="w-12 text-center font-semibold text-lg">
                                {item.quantity}
                              </span>
                              <button
                                onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                                disabled={isProcessing}
                                className="w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors duration-200 disabled:opacity-50"
                              >
                                <PlusIcon size={16} />
                              </button>
                            </div>
                          </div>
                          <div className="text-right">
                            {/* Show original total if discounted */}
                            {product.original_price && (
                              <p className="text-sm text-gray-500 line-through">
                                AED {(product.original_price * item.quantity).toFixed(2)}
                              </p>
                            )}
                            <p className="text-lg font-bold text-gray-900">
                              AED {(product.price * item.quantity).toFixed(2)}
                            </p>
                            {/* Show total savings for this item */}
                            {product.original_price && (
                              <p className="text-sm text-brand-accent-teal font-semibold">
                                Save AED {((product.original_price - product.price) * item.quantity).toFixed(2)}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Cart Summary */}
            <div className="lg:col-span-1">
              <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-white/50 p-6 sticky top-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Order Summary</h3>
                
                <div className="space-y-4 mb-6">
                  {/* Show original total if there are savings */}
                  {cartSummary.totalSavings && cartSummary.totalSavings > 0 && (
                    <div className="flex justify-between">
                      <span className="text-gray-500">Original Total</span>
                      <span className="text-gray-500 line-through">AED {cartSummary.originalTotal?.toFixed(2)}</span>
                    </div>
                  )}

                  {/* Show each product with quantity instead of generic subtotal */}
                  <div className="space-y-2">
                    {cartItems.map((item) => {
                      const product = item.product;
                      if (!product) return null;

                      return (
                        <div key={item.id} className="flex justify-between items-start text-sm gap-2">
                          <span className="text-gray-600 flex-1 leading-relaxed">
                            <span className="block sm:inline">{product.name}</span>
                            <span className="block sm:inline sm:ml-1">x {item.quantity}</span>
                          </span>
                          <span className="font-semibold text-right flex-shrink-0">
                            AED {(product.price * item.quantity).toFixed(2)}
                          </span>
                        </div>
                      );
                    })}
                  </div>

                  {/* Show total savings */}
                  {cartSummary.totalSavings && cartSummary.totalSavings > 0 && (
                    <div className="flex justify-between border-t pt-2">
                      <span className="text-brand-accent-teal font-semibold">Total Savings</span>
                      <span className="text-brand-accent-teal font-bold">-AED {cartSummary.totalSavings.toFixed(2)}</span>
                    </div>
                  )}

                  <div className="border-t pt-4">
                    <div className="flex justify-between text-xl font-bold">
                      <span>Total</span>
                      <span className="text-brand-main-red">AED {cartSummary.total.toFixed(2)}</span>
                    </div>

                    {/* Highlight total savings */}
                    {cartSummary.totalSavings && cartSummary.totalSavings > 0 && (
                      <div className="mt-2 text-center">
                        <span className="bg-brand-accent-teal text-white px-4 py-2 rounded-full text-sm font-bold">
                          🎉 You saved AED {cartSummary.totalSavings.toFixed(2)} total!
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                <button
                  className="w-full bg-gradient-to-r from-brand-main-red to-brand-main-red-darker text-white py-4 rounded-full font-bold text-lg hover:shadow-lg transform hover:scale-105 transition-all duration-300 mb-4"
                  onClick={() => {
                    // Placeholder for checkout functionality
                    alert('Checkout functionality will be implemented soon!');
                  }}
                >
                  Proceed to Checkout
                </button>

                <button
                  onClick={() => navigate('/store')}
                  className="w-full bg-transparent border-2 border-brand-accent-teal text-brand-accent-teal py-3 rounded-full font-semibold hover:bg-brand-accent-teal hover:text-white transition-all duration-300"
                >
                  Continue Shopping
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CartPage;
