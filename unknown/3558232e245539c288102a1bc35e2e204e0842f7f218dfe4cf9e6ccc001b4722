
import React from 'react';

const Footer: React.FC = () => {
  const menuLinks = [
    { name: 'Home', href: '#' },
    { name: 'About Us', href: '#' },
    { name: 'Store', href: '#' },
    { name: 'Contact', href: '#' }
  ];

  const socialLinks = [
    {
      name: 'Instagram',
      href: '#',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
        </svg>
      )
    },
    {
      name: 'LinkedIn',
      href: '#',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
        </svg>
      )
    },
    {
      name: 'WhatsApp',
      href: '#',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.89 3.488"/>
        </svg>
      )
    }
  ];

  return (
    <footer className="relative bg-gradient-to-br from-neutral-900 via-neutral-800 to-neutral-900 py-16 md:py-20 font-sans overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-10 left-10 w-32 h-32 bg-brand-accent-teal/5 rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-20 right-20 w-40 h-40 bg-brand-main-red/5 rounded-full blur-3xl animate-pulse-slow animation-delay-700"></div>
        <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-white/3 rounded-full blur-2xl animate-pulse-slow animation-delay-1000"></div>
      </div>

      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-3xl shadow-2xl p-8 md:p-12 lg:p-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12 mb-12">

            {/* Enhanced Brand Section with Logo */}
            <div className="lg:col-span-1">
              <div className="mb-6">
                <img
                  src="/assets/logo.png"
                  alt="Le Prestine Logo"
                  className="h-12 w-auto mb-4 transition-all duration-300 hover:scale-105"
                />
                <p className="text-gray-300 text-sm leading-relaxed mb-4">
                  Premium quality cleaning products for your home and business.
                  Trusted by thousands of customers worldwide.
                </p>
              </div>

              {/* Contact Info with Modern Icons */}
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="w-5 h-5 bg-brand-accent-teal/20 rounded-lg flex items-center justify-center mt-0.5">
                    <svg className="w-3 h-3 text-brand-accent-teal" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                    </svg>
                  </div>
                  <p className="text-gray-300 text-sm leading-relaxed">
                    Rotonda Giuliani 3 Bianco veneto,<br />
                    62383 Bergamo (VS)
                  </p>
                </div>

                <div className="flex items-center gap-3">
                  <div className="w-5 h-5 bg-brand-accent-teal/20 rounded-lg flex items-center justify-center">
                    <svg className="w-3 h-3 text-brand-accent-teal" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                    </svg>
                  </div>
                  <a href="mailto:<EMAIL>" className="text-gray-300 text-sm hover:text-brand-accent-teal transition-colors duration-300">
                    <EMAIL>
                  </a>
                </div>
              </div>
            </div>

            {/* Enhanced Navigation Menu */}
            <div>
              <h5 className="font-semibold text-white text-lg mb-6 relative">
                <span className="relative z-10">Navigation</span>
                <div className="absolute bottom-0 left-0 w-8 h-0.5 bg-brand-accent-teal rounded-full"></div>
              </h5>
              <ul className="space-y-3">
                {menuLinks.map((link) => (
                  <li key={link.name}>
                    <a
                      href={link.href}
                      className="group flex items-center gap-2 text-gray-300 hover:text-white transition-all duration-300 text-sm py-1"
                    >
                      <div className="w-1 h-1 bg-brand-accent-teal rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <span className="group-hover:translate-x-1 transition-transform duration-300">{link.name}</span>
                    </a>
                  </li>
                ))}
              </ul>
            </div>



            {/* Enhanced Social Links */}
            <div>
              <h5 className="font-semibold text-white text-lg mb-6 relative">
                <span className="relative z-10">Connect With Us</span>
                <div className="absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-brand-accent-teal to-brand-main-red rounded-full"></div>
              </h5>
              <div className="space-y-4">
                {socialLinks.map((link) => (
                  <a
                    key={link.name}
                    href={link.href}
                    className="group flex items-center gap-3 text-gray-300 hover:text-white transition-all duration-300 text-sm py-2 px-3 rounded-xl hover:bg-white/5"
                  >
                    <div className="w-8 h-8 bg-gradient-to-br from-brand-accent-teal/20 to-brand-main-red/20 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <div className="text-brand-accent-teal group-hover:text-white transition-colors duration-300">
                        {link.icon}
                      </div>
                    </div>
                    <span className="group-hover:translate-x-1 transition-transform duration-300">{link.name}</span>
                  </a>
                ))}
              </div>
            </div>
          </div>

          {/* Enhanced Footer Bottom */}
          <div className="border-t border-white/10 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">

              {/* Copyright */}
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-brand-accent-teal rounded-full animate-pulse-slow"></div>
                <p className="text-gray-400 text-sm">
                  &copy; {new Date().getFullYear()} Le Prestine. All Rights Reserved.
                </p>
              </div>

              {/* Additional Links */}
              <div className="flex items-center gap-6 text-xs">
                <a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">
                  Terms of Service
                </a>
                <span className="text-gray-600">•</span>
                <a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">
                  Privacy Policy
                </a>
              </div>

              {/* Back to Top Button */}
              <button
                onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                className="group flex items-center gap-2 px-4 py-2 bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 rounded-full text-gray-400 hover:text-white transition-all duration-300 text-xs"
              >
                <span>Back to Top</span>
                <div className="w-4 h-4 bg-brand-accent-teal/20 rounded-full flex items-center justify-center group-hover:bg-brand-accent-teal/30 transition-colors duration-300">
                  <span className="text-brand-accent-teal text-xs transform group-hover:-translate-y-0.5 transition-transform duration-300">↑</span>
                </div>
              </button>
            </div>

            {/* Decorative Bottom Line */}
            <div className="mt-6 pt-4 border-t border-white/5">
              <div className="flex justify-center">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-px bg-gradient-to-r from-transparent via-brand-accent-teal to-transparent"></div>
                  <div className="w-2 h-2 bg-brand-accent-teal rounded-full animate-pulse-slow"></div>
                  <div className="w-8 h-px bg-gradient-to-r from-transparent via-brand-main-red to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
