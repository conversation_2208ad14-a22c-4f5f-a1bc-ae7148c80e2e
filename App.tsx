
import React from 'react';
import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';
import { AuthProvider } from '@/contexts/AuthContext';
import { CartProvider } from '@/contexts/CartContext';
import Header from '@/components/Header';
import Hero from '@/components/Hero';
import CategorySection from '@/components/CategorySection';
import DiscountedProducts from '@/components/DiscountedProducts';
import WhyChooseSection from '@/components/WhyChooseSection';
import ContactUs from '@/components/ContactUs';
import Footer from '@/components/Footer';
import StorePage from '@/components/StorePage';
import AboutUsPage from '@/components/AboutUsPage';
import ProductDetailPage from '@/components/ProductDetailPage';
import CartPage from '@/components/CartPage';
import LoginPage from '@/components/auth/LoginPage';
import SignupPage from '@/components/auth/SignupPage';
import ProfilePage from '@/components/auth/ProfilePage';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

// Home page component
const HomePage: React.FC = () => (
  <>
    <Hero />
    <CategorySection />
    <DiscountedProducts />
    <WhyChooseSection />
    <ContactUs />
  </>
);

// Layout component that includes Header and Footer
const Layout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const location = useLocation();

  // Scroll to top on route change
  React.useEffect(() => {
    window.scrollTo(0, 0);
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <Header />
      <main className="flex-grow">
        {children}
      </main>
      <Footer />
    </div>
  );
};

const App: React.FC = () => {
  return (
    <AuthProvider>
      <CartProvider>
        <Router>
          <Layout>
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/store" element={<StorePage />} />
              <Route path="/about" element={<AboutUsPage />} />
              <Route path="/product/:productSlug" element={<ProductDetailPage />} />
              <Route path="/cart" element={
                <ProtectedRoute>
                  <CartPage />
                </ProtectedRoute>
              } />
              <Route path="/login" element={<LoginPage />} />
              <Route path="/signup" element={<SignupPage />} />
              <Route path="/profile" element={
                <ProtectedRoute>
                  <ProfilePage />
                </ProtectedRoute>
              } />
              {/* Redirect any unknown routes to home */}
              <Route path="*" element={<HomePage />} />
            </Routes>
          </Layout>
        </Router>
      </CartProvider>
    </AuthProvider>
  );
};

export default App;
