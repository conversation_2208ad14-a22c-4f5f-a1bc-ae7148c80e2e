import React, { useRef, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowRightIcon } from './common/Icon';

interface DiscountedProduct {
  id: string;
  name: string;
  description: string;
  imageUrl: string;
}

const discountedProducts: DiscountedProduct[] = [
  {
    id: '1',
    name: 'Abaya Shampoo',
    description: 'Gentle formula for delicate fabrics',
    imageUrl: '/assets/products/abaya-shampoo.jpg',
  },
  {
    id: '2',
    name: 'Detergent Powder',
    description: 'Powerful cleaning for all fabrics',
    imageUrl: '/assets/products/detergent-powder.jpg',
  },
  {
    id: '3',
    name: 'Dishwash Liquid',
    description: 'Cuts through grease effectively',
    imageUrl: '/assets/products/dishwash-liquid.jpg',
  },
  {
    id: '4',
    name: 'Fabric Softener',
    description: 'Leaves clothes soft and fresh',
    imageUrl: '/assets/products/fabric-softner.jpg',
  },
  {
    id: '5',
    name: '<PERSON> Was<PERSON>',
    description: 'Gentle care for your hands',
    imageUrl: '/assets/products/handwash-1.jpg',
  },
  {
    id: '6',
    name: 'Laundry Detergent',
    description: 'Complete laundry care solution',
    imageUrl: '/assets/products/laundry-detergent.jpg',
  },
  {
    id: '7',
    name: 'Taze',
    description: 'Fresh and clean results',
    imageUrl: '/assets/products/taze.jpg',
  },
  {
    id: '8',
    name: 'Toilet Cleaner',
    description: 'Deep cleaning for bathrooms',
    imageUrl: '/assets/products/toilet-cleaner.jpg',
  },
  {
    id: '9',
    name: 'Emulsifiers',
    description: 'Professional cleaning solution',
    imageUrl: '/assets/products/emulsifiers.jpg',
  },
];

const DiscountedProducts: React.FC = () => {
  const navigate = useNavigate();
  const [isVisible, setIsVisible] = useState(false);
  const [hasAnimated, setHasAnimated] = useState(false);
  const [animationComplete, setAnimationComplete] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  // Intersection Observer for scroll-triggered animation
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !hasAnimated) {
            setIsVisible(true);
            setHasAnimated(true);

            // Mark animation as complete after all cards have finished animating
            // Last card starts at 700ms + (8 * 150ms) = 1900ms, plus 700ms duration = 2600ms total
            setTimeout(() => {
              setAnimationComplete(true);
            }, 3000);
          }
        });
      },
      {
        threshold: 0.15,
        rootMargin: '0px 0px -80px 0px'
      }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, [hasAnimated]);

  return (
    <section
      ref={sectionRef}
      className="bg-brand-accent-teal pt-10 sm:pt-14 md:pt-[4.5rem] pb-12 md:pb-16 overflow-hidden">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className={`mb-8 md:mb-12 text-center sm:text-left transition-all duration-1200 ease-out ${
          isVisible
            ? 'opacity-100 translate-y-0 scale-100'
            : 'opacity-0 translate-y-12 scale-95'
        }`} style={{ transitionDelay: isVisible ? '100ms' : '0ms' }}>
            <h2 className={`font-serif text-4xl sm:text-5xl md:text-6xl font-bold text-white mb-4 sm:mb-6 leading-tight transition-all duration-1000 ease-out ${
              isVisible
                ? 'opacity-100 translate-y-0 rotate-0'
                : 'opacity-0 translate-y-8 -rotate-1'
            }`} style={{
              transitionDelay: isVisible ? '300ms' : '0ms',
              textShadow: '0 4px 20px rgba(0,0,0,0.3)'
            }}>
              Transform Your Home Into a Sanctuary
            </h2>
            <p className={`text-white/90 text-lg sm:text-xl leading-relaxed max-w-3xl transition-all duration-1000 ease-out ${
              isVisible
                ? 'opacity-100 translate-x-0'
                : 'opacity-0 -translate-x-8'
            }`} style={{ transitionDelay: isVisible ? '500ms' : '0ms' }}>
              Discover our entire collection of premium cleaning solutions, meticulously crafted to deliver exceptional results for every cleaning need in your home.
            </p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
          {discountedProducts.map((product, index) => (
            <div
              key={product.id}
              className={`bg-white/90 backdrop-blur-sm rounded-2xl group flex flex-col font-sans shadow-lg border border-white/50 transform-gpu ${
                // Entrance animation classes
                isVisible
                  ? 'opacity-100 translate-y-0 scale-100 rotate-0'
                  : 'opacity-0 translate-y-16 scale-90 rotate-3'
              } ${
                // Hover effects only after animation is complete
                animationComplete
                  ? 'hover:shadow-xl hover:bg-white/100 hover:scale-[1.02] hover:z-10 transition-all duration-300 ease-out'
                  : ''
              }`}
              style={{
                // Entrance animation timing
                transition: !animationComplete
                  ? `all 700ms ease-out ${isVisible ? `${700 + (index * 150)}ms` : '0ms'}`
                  : undefined,
                transformOrigin: 'center bottom'
              }}>
              <div className="relative bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl aspect-square overflow-hidden">
                <div className="absolute inset-0 opacity-5" style={{
                  backgroundImage: `radial-gradient(circle at 2px 2px, rgba(9, 184, 166, 0.3) 1px, transparent 0)`,
                  backgroundSize: '20px 20px'
                }}></div>

                <img
                  src={product.imageUrl}
                  alt={product.name}
                  className={`relative z-10 w-full h-full object-cover transform-gpu ${
                    // Entrance animation classes
                    isVisible
                      ? 'opacity-100 scale-100 rotate-0'
                      : 'opacity-0 scale-75 rotate-6'
                  } ${
                    // Hover effects only after animation is complete
                    animationComplete
                      ? 'group-hover:scale-[1.03] transition-all duration-300 ease-out'
                      : ''
                  }`}
                  style={{
                    // Entrance animation timing and effects
                    transition: !animationComplete
                      ? `all 1000ms ease-out ${isVisible ? `${900 + (index * 150)}ms` : '0ms'}`
                      : undefined,
                    filter: !animationComplete ? (isVisible ? 'blur(0px)' : 'blur(4px)') : 'blur(0px)'
                  }}
                />

                <div className="absolute top-2 left-2 w-3 h-3 bg-brand-accent-teal/20 rounded-full blur-sm opacity-0 group-hover:opacity-60 transition-opacity duration-500"></div>
                <div className="absolute bottom-2 right-2 w-2 h-2 bg-brand-main-red/20 rounded-full blur-sm opacity-0 group-hover:opacity-40 transition-opacity duration-500"></div>
              </div>

              <div className="pt-4 pb-5 px-4 text-left relative">
                <div className="absolute inset-0 bg-gradient-to-t from-gray-50/50 to-transparent rounded-b-2xl"></div>

                <div className="relative z-10">
                  <h3
                    className={`text-lg font-bold text-gray-900 mb-2 transform-gpu ${
                      // Entrance animation classes
                      isVisible
                        ? 'opacity-100 translate-y-0'
                        : 'opacity-0 translate-y-4'
                    } ${
                      // Hover effects only after animation is complete
                      animationComplete
                        ? 'group-hover:text-brand-accent-teal transition-all duration-300 ease-out'
                        : ''
                    }`}
                    style={{
                      fontWeight: 700,
                      // Entrance animation timing
                      transition: !animationComplete
                        ? `all 800ms ease-out ${isVisible ? `${1100 + (index * 150)}ms` : '0ms'}`
                        : undefined
                    }}
                  >
                    {product.name}
                  </h3>
                  <p className={`text-sm text-gray-600 leading-relaxed transform-gpu ${
                    // Entrance animation classes
                    isVisible
                      ? 'opacity-100 translate-y-0'
                      : 'opacity-0 translate-y-3'
                  } ${
                    // Hover effects only after animation is complete
                    animationComplete
                      ? 'group-hover:text-gray-800 transition-all duration-300 ease-out'
                      : ''
                  }`}
                  style={{
                    // Entrance animation timing
                    transition: !animationComplete
                      ? `all 800ms ease-out ${isVisible ? `${1200 + (index * 150)}ms` : '0ms'}`
                      : undefined
                  }}>
                    {product.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
        {/* Enhanced Call-to-Action Section */}
        <div className={`mt-12 md:mt-16 relative transition-all duration-1000 ease-out ${
          isVisible
            ? 'opacity-100 translate-y-0 scale-100'
            : 'opacity-0 translate-y-8 scale-90'
        }`} style={{ transitionDelay: isVisible ? '2000ms' : '0ms' }}>

          {/* Background accent */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent rounded-3xl blur-xl"></div>

          {/* Main CTA Container */}
          <div className="relative bg-white/15 backdrop-blur-md rounded-3xl p-8 md:p-12 border border-white/30 shadow-2xl">
            <div className="text-center">
              <h3 className="font-serif text-2xl md:text-3xl font-bold text-white mb-4 leading-tight">
                Ready to Transform Your Home?
              </h3>
              <p className="text-white/90 text-lg mb-8 max-w-2xl mx-auto leading-relaxed">
                Explore our complete collection and find the perfect cleaning solutions for every corner of your home.
              </p>

              <button
                onClick={() => navigate('/store')}
                className={`
                  group relative
                  bg-gradient-to-r from-brand-main-red to-brand-main-red-darker
                  text-white font-bold text-xl
                  py-4 px-12 rounded-2xl
                  shadow-2xl hover:shadow-3xl
                  transform hover:scale-105 active:scale-95 hover:-rotate-1
                  transition-all duration-500 ease-out
                  focus:outline-none focus:ring-4 focus:ring-white/30
                  flex items-center justify-center space-x-3 mx-auto
                  hover:shadow-brand-main-red/40
                  border-2 border-white/20 hover:border-white/40
                  min-w-[200px]
                  ${isVisible ? 'animate-bounce' : ''}
                `}
                style={{
                  animation: isVisible ? 'bounce 2s infinite' : 'none',
                  animationDelay: '2500ms',
                  boxShadow: '0 20px 40px rgba(0,0,0,0.3), 0 0 0 1px rgba(255,255,255,0.1) inset'
                }}
                aria-label="Go to store page"
              >
                {/* Button glow effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                <span className="relative z-10 tracking-wide">Explore Our Store</span>
                <ArrowRightIcon
                  size={24}
                  strokeWidth={2.5}
                  className="relative z-10 transition-transform duration-300 ease-in-out group-hover:translate-x-2 group-hover:scale-110"
                />

                {/* Animated background shimmer */}
                <div className="absolute inset-0 rounded-2xl overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DiscountedProducts;