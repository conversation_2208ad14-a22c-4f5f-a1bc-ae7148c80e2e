import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from './AuthContext';
import { CartItem, CartSummary, Product } from '../types';

interface CartContextType {
  cartItems: CartItem[];
  cartSummary: CartSummary;
  loading: boolean;
  addToCart: (productId: string, quantity?: number) => Promise<{ success: boolean; error?: string }>;
  removeFromCart: (cartItemId: string) => Promise<{ success: boolean; error?: string }>;
  updateQuantity: (cartItemId: string, quantity: number) => Promise<{ success: boolean; error?: string }>;
  clearCart: () => Promise<{ success: boolean; error?: string }>;
  refreshCart: () => Promise<void>;
  isInCart: (productId: string) => boolean;
  getCartItemQuantity: (productId: string) => number;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

interface CartProviderProps {
  children: React.ReactNode;
}

export const CartProvider: React.FC<CartProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [loading, setLoading] = useState(false);

  // Calculate cart summary
  const cartSummary: CartSummary = React.useMemo(() => {
    const subtotal = cartItems.reduce((sum, item) => {
      const price = item.product?.price || 0;
      return sum + (price * item.quantity);
    }, 0);

    // Calculate total savings
    const totalSavings = cartItems.reduce((sum, item) => {
      const originalPrice = item.product?.original_price || item.product?.price || 0;
      const currentPrice = item.product?.price || 0;
      const savings = originalPrice - currentPrice;
      return sum + (savings * item.quantity);
    }, 0);

    // Calculate what the original total would have been
    const originalTotal = cartItems.reduce((sum, item) => {
      const originalPrice = item.product?.original_price || item.product?.price || 0;
      return sum + (originalPrice * item.quantity);
    }, 0);

    const total = subtotal; // No tax calculation
    const itemCount = cartItems.reduce((sum, item) => sum + item.quantity, 0);

    return {
      subtotal: Number(subtotal.toFixed(2)),
      tax: 0, // No tax
      total: Number(total.toFixed(2)),
      itemCount,
      totalSavings: Number(totalSavings.toFixed(2)),
      originalTotal: Number(originalTotal.toFixed(2))
    };
  }, [cartItems]);

  // Fetch cart items from database
  const fetchCartItems = async () => {
    if (!user) {
      setCartItems([]);
      return;
    }

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('cart_items')
        .select(`
          *,
          product:products(
            id,
            name,
            category,
            price,
            original_price,
            discount,
            image_url,
            description,
            material,
            features
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching cart items:', error);
        return;
      }

      setCartItems(data || []);
    } catch (error) {
      console.error('Unexpected error fetching cart:', error);
    } finally {
      setLoading(false);
    }
  };

  // Add item to cart
  const addToCart = async (productId: string, quantity: number = 1) => {
    if (!user) {
      return { success: false, error: 'Please log in to add items to cart' };
    }

    try {
      // Use PostgreSQL's ON CONFLICT to handle upsert properly
      const { data, error } = await supabase.rpc('upsert_cart_item', {
        p_user_id: user.id,
        p_product_id: productId,
        p_quantity: quantity
      });

      if (error) {
        // Fallback to manual check and insert/update
        const { data: existingItems, error: fetchError } = await supabase
          .from('cart_items')
          .select('*')
          .eq('user_id', user.id)
          .eq('product_id', productId)
          .limit(1);

        if (fetchError) {
          console.error('Error checking existing cart item:', fetchError);
          return { success: false, error: 'Failed to check cart' };
        }

        if (existingItems && existingItems.length > 0) {
          // Update existing item quantity
          const existingItem = existingItems[0];
          const newQuantity = existingItem.quantity + quantity;

          const { error: updateError } = await supabase
            .from('cart_items')
            .update({ quantity: newQuantity })
            .eq('id', existingItem.id);

          if (updateError) {
            console.error('Error updating cart item:', updateError);
            return { success: false, error: 'Failed to update cart item' };
          }
        } else {
          // Insert new item
          const { error: insertError } = await supabase
            .from('cart_items')
            .insert({
              user_id: user.id,
              product_id: productId,
              quantity
            });

          if (insertError) {
            console.error('Error adding to cart:', insertError);
            return { success: false, error: 'Failed to add item to cart' };
          }
        }
      }

      await fetchCartItems();
      return { success: true };
    } catch (error) {
      console.error('Unexpected error adding to cart:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  };

  // Remove item from cart
  const removeFromCart = async (cartItemId: string) => {
    if (!user) {
      return { success: false, error: 'Please log in to manage cart' };
    }

    try {
      const { error } = await supabase
        .from('cart_items')
        .delete()
        .eq('id', cartItemId)
        .eq('user_id', user.id);

      if (error) {
        console.error('Error removing from cart:', error);
        return { success: false, error: 'Failed to remove item from cart' };
      }

      await fetchCartItems();
      return { success: true };
    } catch (error) {
      console.error('Unexpected error removing from cart:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  };

  // Update item quantity
  const updateQuantity = async (cartItemId: string, quantity: number) => {
    if (!user) {
      return { success: false, error: 'Please log in to manage cart' };
    }

    if (quantity <= 0) {
      return removeFromCart(cartItemId);
    }

    try {
      const { error } = await supabase
        .from('cart_items')
        .update({ quantity })
        .eq('id', cartItemId)
        .eq('user_id', user.id);

      if (error) {
        console.error('Error updating quantity:', error);
        return { success: false, error: 'Failed to update quantity' };
      }

      await fetchCartItems();
      return { success: true };
    } catch (error) {
      console.error('Unexpected error updating quantity:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  };

  // Clear entire cart
  const clearCart = async () => {
    if (!user) {
      return { success: false, error: 'Please log in to manage cart' };
    }

    try {
      const { error } = await supabase
        .from('cart_items')
        .delete()
        .eq('user_id', user.id);

      if (error) {
        console.error('Error clearing cart:', error);
        return { success: false, error: 'Failed to clear cart' };
      }

      setCartItems([]);
      return { success: true };
    } catch (error) {
      console.error('Unexpected error clearing cart:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  };

  // Refresh cart data
  const refreshCart = async () => {
    await fetchCartItems();
  };

  // Check if product is in cart
  const isInCart = (productId: string): boolean => {
    return cartItems.some(item => item.product_id === productId);
  };

  // Get quantity of product in cart
  const getCartItemQuantity = (productId: string): number => {
    const item = cartItems.find(item => item.product_id === productId);
    return item ? item.quantity : 0;
  };

  // Fetch cart items when user changes
  useEffect(() => {
    fetchCartItems();
  }, [user]);

  const value = {
    cartItems,
    cartSummary,
    loading,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    refreshCart,
    isInCart,
    getCartItemQuantity,
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};
