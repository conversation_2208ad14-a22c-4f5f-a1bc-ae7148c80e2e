
import React, { useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import ProductCard from './ProductCard';
import { Product } from '../types';
import { ChevronDownIcon } from './common/Icon';
import { supabase } from '../lib/supabase';

// Product data using local images from assets/products folder
// This includes ALL products from both database and mock data
const initialMockProducts: Product[] = [
  // Database products (sp1-sp10)
  {
    id: 'sp1',
    name: 'Abaya Shampoo',
    material: 'Gentle Formula for Delicate Fabrics',
    price: 24.99,
    originalPrice: 34.99,
    discount: '30% OFF',
    imageUrl: '/assets/products/abaya-shampoo.jpg',
    category: 'Laundry & Fabric Care',
    description: 'Gentle formula specially designed for delicate fabrics like abayas',
    features: ['Gentle', 'Fabric Safe'],
  },
  {
    id: 'sp2',
    name: 'Premium Detergent Powder',
    material: 'Concentrated Cleaning Formula',
    price: 18.50,
    originalPrice: 25.99,
    discount: '25% OFF',
    imageUrl: '/assets/products/detergent-powder.jpg',
    category: 'Laundry & Fabric Care',
    description: 'Concentrated cleaning formula for effective stain removal',
    features: ['Concentrated', 'Stain Removal'],
  },
  {
    id: 'sp3',
    name: 'Fabric Softener',
    material: 'Premium Softening Formula',
    price: 15.75,
    imageUrl: '/assets/products/fabric-softner.jpg',
    category: 'Laundry & Fabric Care',
    description: 'Premium fabric softener for ultimate comfort',
    features: ['Softening', 'Fresh Scent'],
  },
  {
    id: 'sp4',
    name: 'Stain Remover Spray',
    material: 'Advanced Stain Fighting Technology',
    price: 12.99,
    originalPrice: 18.99,
    discount: '35% OFF',
    imageUrl: '/assets/products/fabric-softner.jpg', // Using fabric softener as placeholder
    category: 'Laundry & Fabric Care',
    description: 'Powerful stain removal for tough stains',
    features: ['Powerful', 'Quick Action'],
  },
  {
    id: 'sp5',
    name: 'Delicate Wash',
    material: 'Ultra-Gentle Cleaning Formula',
    price: 21.25,
    originalPrice: 29.99,
    discount: '30% OFF',
    imageUrl: '/assets/products/fabric-softner.jpg', // Using fabric softener as placeholder
    category: 'Laundry & Fabric Care',
    description: 'Gentle care for your most precious garments',
    features: ['Gentle', 'Color Protection'],
  },
  {
    id: 'sp6',
    name: 'Multi-Surface Cleaner',
    material: 'All-Purpose Cleaning Solution',
    price: 16.99,
    imageUrl: '/assets/products/taze.jpg', // Using taze as placeholder
    category: 'Home & Surface Care',
    description: 'Versatile cleaner for all surfaces',
    features: ['Versatile', 'Streak-Free'],
  },
  {
    id: 'sp7',
    name: 'Laundry Detergent',
    material: 'Advanced Cleaning Technology',
    price: 22.50,
    originalPrice: 32.99,
    discount: '35% OFF',
    imageUrl: '/assets/products/laundry-detergent.jpg',
    category: 'Laundry & Fabric Care',
    description: 'Advanced cleaning technology with color protection',
    features: ['Advanced Formula', 'Color Protection'],
  },
  {
    id: 'sp8',
    name: 'Glass Cleaner',
    material: 'Streak-Free Glass Formula',
    price: 13.75,
    imageUrl: '/assets/products/taze.jpg', // Using taze as placeholder
    category: 'Home & Surface Care',
    description: 'Crystal clear results for all glass surfaces',
    features: ['Streak-Free', 'Crystal Clear'],
  },
  {
    id: 'sp9',
    name: 'Floor Cleaner',
    material: 'Multi-Floor Cleaning Formula',
    price: 19.99,
    originalPrice: 27.99,
    discount: '30% OFF',
    imageUrl: '/assets/products/taze.jpg', // Using taze as placeholder
    category: 'Home & Surface Care',
    description: 'Deep cleaning power for all floor types',
    features: ['Deep Clean', 'Fresh Scent'],
  },
  {
    id: 'sp10',
    name: 'Bathroom Cleaner',
    material: 'Anti-Bacterial Formula',
    price: 17.50,
    originalPrice: 24.99,
    discount: '30% OFF',
    imageUrl: '/assets/products/toilet-cleaner.jpg',
    category: 'Home & Surface Care',
    description: 'Powerful bathroom cleaning solution',
    features: ['Anti-Bacterial', 'Lime Scale Removal'],
  },
  // Additional products from original mock data (sp11-sp15)
  {
    id: 'sp11',
    name: 'Dishwash Liquid',
    material: 'Grease-Cutting Formula',
    price: 12.75,
    originalPrice: 18.99,
    discount: '35% OFF',
    imageUrl: '/assets/products/dishwash-liquid.jpg',
    category: 'Home & Surface Care',
    description: 'Powerful grease-cutting formula for sparkling clean dishes',
    features: ['Grease-Cutting', 'Gentle on Hands'],
  },
  {
    id: 'sp12',
    name: 'Professional Emulsifiers',
    material: 'Industrial Grade Cleaning Agent',
    price: 32.00,
    originalPrice: 18.99,
    discount: '35% OFF',
    imageUrl: '/assets/products/emulsifiers.jpg',
    category: 'Home & Surface Care',
    description: 'Industrial grade emulsifiers for professional cleaning',
    features: ['Industrial Grade', 'Multi-Purpose'],
  },
  {
    id: 'sp13',
    name: 'Gentle Hand Wash',
    material: 'Moisturizing Hand Care Formula',
    price: 14.99,
    imageUrl: '/assets/products/handwash-1.jpg',
    category: 'Personal Care',
    description: 'Moisturizing hand care formula with antibacterial protection',
    features: ['Moisturizing', 'Antibacterial'],
    variants: [
      {
        id: 'green-apple',
        name: 'Green Apple',
        imageUrl: '/assets/products/handwash-1.jpg',
        color: { name: 'Green', hex: '#22C55E' }
      },
      {
        id: 'strawberry',
        name: 'Strawberry',
        imageUrl: '/assets/products/handwash-2.jpg',
        color: { name: 'Red', hex: '#EF4444' }
      },
      {
        id: 'lavender',
        name: 'Lavender',
        imageUrl: '/assets/products/handwash-3.jpg',
        color: { name: 'Purple', hex: '#A855F7' }
      }
    ],
    selectedVariant: 'green-apple',
    availableColorCount: 3,
  },
  {
    id: 'sp14',
    name: 'Taze Fresh Cleaner',
    material: 'All-Purpose Cleaning Solution',
    price: 19.75,
    imageUrl: '/assets/products/taze.jpg',
    category: 'Home & Surface Care',
    description: 'Fresh all-purpose cleaning solution for multiple surfaces',
    features: ['Fresh Scent', 'Multi-Surface'],
  },
  {
    id: 'sp15',
    name: 'Toilet Cleaner',
    material: 'Powerful Disinfectant Formula',
    price: 13.25,
    originalPrice: 27.99,
    discount: '30% OFF',
    imageUrl: '/assets/products/toilet-cleaner.jpg',
    category: 'Home & Surface Care',
    description: 'Powerful disinfectant formula for deep toilet cleaning',
    features: ['Disinfectant', 'Deep Clean'],
  },
];

interface FilterDropdownProps {
  title: string;
  currentSelection: string;
  options: string[];
  onSelect: (option: string) => void;
  ariaLabel: string;
}

const FilterDropdown: React.FC<FilterDropdownProps> = ({ title, currentSelection, options, onSelect, ariaLabel }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [buttonRect, setButtonRect] = useState<DOMRect | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Update button position when dropdown opens
  useEffect(() => {
    if (isOpen && buttonRef.current) {
      const updatePosition = (event?: Event) => {
        // Don't update position if scrolling within the dropdown
        if (event && dropdownRef.current && dropdownRef.current.contains(event.target as Node)) {
          return;
        }

        if (buttonRef.current) {
          setButtonRect(buttonRef.current.getBoundingClientRect());
        }
      };

      updatePosition();

      // Update position on scroll or resize, but not when scrolling within dropdown
      document.addEventListener('scroll', updatePosition, true);
      window.addEventListener('resize', updatePosition);

      return () => {
        document.removeEventListener('scroll', updatePosition, true);
        window.removeEventListener('resize', updatePosition);
      };
    }
  }, [isOpen]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) &&
          buttonRef.current && !buttonRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    const handleScroll = (event: Event) => {
      // Only close dropdown if scrolling is happening outside the dropdown
      if (isOpen && dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('scroll', handleScroll, true);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
        document.removeEventListener('scroll', handleScroll, true);
      };
    }
  }, [isOpen]);

  const dropdownContent = isOpen && buttonRect && (
    <div
      ref={dropdownRef}
      className="fixed bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 max-h-60 overflow-y-auto custom-dropdown-scrollbar"
      style={{
        top: buttonRect.bottom + 8,
        left: buttonRect.left,
        width: buttonRect.width,
        zIndex: 999999,
        // Custom scrollbar styles for Firefox
        scrollbarWidth: 'thin',
        scrollbarColor: 'rgba(9, 184, 166, 0.4) transparent'
      }}
    >
      {options.map((option) => (
        <button
          key={option}
          onClick={() => {
            onSelect(option);
            setIsOpen(false);
          }}
          className="w-full text-left px-5 py-3 text-sm font-medium text-gray-800 hover:bg-brand-accent-teal/10 hover:text-brand-accent-teal transition-colors duration-200 first:rounded-t-2xl last:rounded-b-2xl"
        >
          {option}
        </button>
      ))}
    </div>
  );

  return (
    <div className="font-sans group relative">
      <label className="block text-xs font-medium text-gray-600 mb-2 tracking-wide uppercase" htmlFor={title.toLowerCase().replace(' ', '-')}>
        {title}
      </label>
      <button
        ref={buttonRef}
        id={title.toLowerCase().replace(' ', '-')}
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between bg-white/80 backdrop-blur-sm text-gray-800 px-5 py-3.5 rounded-2xl shadow-lg border border-gray-200/50 hover:border-brand-accent-teal/30 hover:shadow-xl hover:bg-white/90 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-brand-accent-teal/50 focus:ring-opacity-50 group-hover:scale-[1.02] transform"
        aria-haspopup="listbox"
        aria-expanded={isOpen}
        aria-label={ariaLabel}
      >
        <span className="text-sm font-medium truncate">{currentSelection}</span>
        <ChevronDownIcon size={18} className={`text-gray-500 ml-3 flex-shrink-0 transition-transform duration-300 ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Portal the dropdown to document.body to escape any overflow constraints */}
      {typeof document !== 'undefined' && createPortal(dropdownContent, document.body)}

      {/* Subtle glow effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-brand-accent-teal/5 to-brand-main-red/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
    </div>
  );
};

const StorePage: React.FC = () => {
  // Animation and visibility states
  const [isHeaderVisible, setIsHeaderVisible] = useState(false);
  const [isFiltersVisible, setIsFiltersVisible] = useState(false);
  const [visibleProducts, setVisibleProducts] = useState<Set<number>>(new Set());

  // Refs for intersection observer
  const headerRef = useRef<HTMLDivElement>(null);
  const filtersRef = useRef<HTMLDivElement>(null);
  const productRefs = useRef<(HTMLDivElement | null)[]>([]);

  // Product filtering and sorting
  const [allProducts, setAllProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  // Filter states - now interactive
  const [category, setCategory] = useState('All Categories');
  const [price, setPrice] = useState('All Prices');
  const [sort, setSort] = useState('New In');

  // Filter options - Updated to match database categories
  const categoryOptions = [
    'All Categories',
    'Laundry & Fabric Care',
    'Home & Surface Care',
    'Personal Care'
  ];
  const priceOptions = ['All Prices', 'Under AED 15', 'AED 15 - AED 25', 'AED 25 - AED 35', 'Over AED 35'];
  const sortOptions = ['New In', 'Price Low-High', 'Price High-Low', 'Name A-Z', 'Name Z-A'];

  // Fetch products from database
  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true);
      try {
        const { data, error } = await supabase
          .from('products')
          .select('*')
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching products:', error);
          // Fallback to mock data if database fails
          setAllProducts(initialMockProducts);
        } else {
          // Transform database products to match frontend interface
          const transformedProducts: Product[] = (data || []).map(product => ({
            ...product,
            imageUrl: product.image_url,
            originalPrice: product.original_price,
          }));
          setAllProducts(transformedProducts);
        }
      } catch (error) {
        console.error('Unexpected error fetching products:', error);
        // Fallback to mock data
        setAllProducts(initialMockProducts);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  // Intersection Observer for scroll-triggered animations
  useEffect(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          if (entry.target === headerRef.current) {
            setIsHeaderVisible(true);
          } else if (entry.target === filtersRef.current) {
            setIsFiltersVisible(true);
          } else {
            // Handle product cards
            const index = productRefs.current.findIndex(ref => ref === entry.target);
            if (index !== -1) {
              setVisibleProducts(prev => new Set([...prev, index]));
            }
          }
        }
      });
    }, observerOptions);

    // Observe header and filters
    if (headerRef.current) observer.observe(headerRef.current);
    if (filtersRef.current) observer.observe(filtersRef.current);

    return () => observer.disconnect();
  }, []);

  // Effect for filtering and sorting
  useEffect(() => {
    let products = [...allProducts];

    // Filter by category
    if (category !== 'All Categories') {
      products = products.filter(product => product.category === category);
    }

    // Filter by price range
    if (price !== 'All Prices') {
      products = products.filter(product => {
        switch (price) {
          case 'Under AED 15':
            return product.price < 15;
          case 'AED 15 - AED 25':
            return product.price >= 15 && product.price <= 25;
          case 'AED 25 - AED 35':
            return product.price >= 25 && product.price <= 35;
          case 'Over AED 35':
            return product.price > 35;
          default:
            return true;
        }
      });
    }

    // Sort products
    switch (sort) {
      case 'Price Low-High':
        products.sort((a, b) => a.price - b.price);
        break;
      case 'Price High-Low':
        products.sort((a, b) => b.price - a.price);
        break;
      case 'Name A-Z':
        products.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'Name Z-A':
        products.sort((a, b) => b.name.localeCompare(a.name));
        break;
      default:
        // 'New In' - keep original order
        break;
    }

    setFilteredProducts(products);
  }, [category, price, sort, allProducts]);

  // Effect to observe product cards when they're rendered
  useEffect(() => {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const index = productRefs.current.findIndex(ref => ref === entry.target);
          if (index !== -1) {
            setVisibleProducts(prev => new Set([...prev, index]));
          }
        }
      });
    }, { threshold: 0.1, rootMargin: '0px 0px -50px 0px' });

    productRefs.current.forEach((ref) => {
      if (ref) observer.observe(ref);
    });

    return () => observer.disconnect();
  }, [filteredProducts]);

  return (
    <div className="min-h-screen font-sans relative overflow-hidden">
      {/* Enhanced Background with Patterns and Glass Morphism */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-page-bg-light to-gray-100">
        {/* Animated Background Patterns */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-brand-accent-teal/15 to-transparent rounded-full blur-3xl animate-parallax-float"></div>
          <div className="absolute bottom-0 left-0 w-80 h-80 bg-gradient-to-tr from-brand-main-red/10 to-transparent rounded-full blur-3xl animate-parallax-float animation-delay-300"></div>
          <div className="absolute top-1/3 left-1/2 w-64 h-64 bg-gradient-to-r from-brand-accent-teal/8 to-brand-main-red/8 rounded-full blur-2xl animate-pulse-slow"></div>
        </div>

        {/* Subtle Grid Pattern */}
        <div className="absolute inset-0 opacity-5" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgba(9, 184, 166, 0.3) 1px, transparent 0)`,
          backgroundSize: '40px 40px'
        }}></div>
      </div>

      <div className="relative container mx-auto px-4 sm:px-6 lg:px-8 py-10 sm:py-12 md:py-16">
        {/* Enhanced Header Section */}
        <div
          ref={headerRef}
          className={`mb-12 md:mb-16 relative ${isHeaderVisible ? 'animate-fadeIn' : 'opacity-0'}`}
        >
          {/* Glass Morphism Background for Header */}
          <div className="absolute inset-0 bg-white/30 backdrop-blur-sm rounded-3xl opacity-60 -m-4 p-4"></div>

          <div className="relative z-10">
            {/* Brand Label */}
            <div className={`mb-6 ${isHeaderVisible ? 'animate-scale-in animation-delay-100' : 'opacity-0'}`}>
              <span className="inline-block px-6 py-3 bg-gradient-to-r from-brand-accent-teal/20 to-brand-main-red/20 backdrop-blur-sm rounded-full text-sm font-semibold tracking-widest uppercase text-brand-accent-teal border border-brand-accent-teal/30">
                Premium Product Collection
              </span>
            </div>

            <h1
              className={`font-serif text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold text-gray-900 mb-6 md:mb-8 leading-tight ${isHeaderVisible ? 'animate-slide-in-left animation-delay-200' : 'opacity-0'}`}
              style={{ fontWeight: 700 }}
            >
              Our Products
              <span className="block bg-gradient-to-r from-brand-accent-teal via-brand-accent-teal-darker to-brand-main-red bg-clip-text text-transparent mt-2">
                Crafted for Excellence
              </span>
            </h1>

            <p className={`text-base sm:text-lg text-gray-700 max-w-4xl leading-relaxed ${isHeaderVisible ? 'animate-slide-in-left animation-delay-300' : 'opacity-0'}`}>
              Discover our complete range of premium cleaning and personal care products. From gentle hand care to powerful cleaning solutions, find everything you need for a spotless home and healthy lifestyle.
            </p>

            {/* Floating Decorative Elements */}
            <div className="absolute -top-4 -right-4 w-20 h-20 bg-brand-accent-teal/10 rounded-full blur-xl animate-parallax-float opacity-60"></div>
            <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-brand-main-red/10 rounded-full blur-lg animate-parallax-float animation-delay-500 opacity-50"></div>
          </div>
        </div>

        {/* Enhanced Filters Section */}
        <div
          ref={filtersRef}
          className={`mb-12 md:mb-16 relative ${isFiltersVisible ? 'animate-fadeIn animation-delay-200' : 'opacity-0'}`}
        >
          {/* Glass Morphism Background for Filters */}
          <div className="absolute inset-0 bg-white/20 backdrop-blur-sm rounded-3xl border border-white/30 shadow-xl -m-6 p-6"></div>

          <div className="relative flex flex-col md:flex-row md:items-end gap-6 p-6">
            <div className="flex-grow grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className={`relative ${isFiltersVisible ? 'animate-scale-in animation-delay-300' : 'opacity-0'}`}>
                <FilterDropdown
                  title="Category"
                  currentSelection={category}
                  options={categoryOptions}
                  onSelect={setCategory}
                  ariaLabel="Filter by category. Current: All Categories"
                />
              </div>
              <div className={`relative ${isFiltersVisible ? 'animate-scale-in animation-delay-400' : 'opacity-0'}`}>
                <FilterDropdown
                  title="Price"
                  currentSelection={price}
                  options={priceOptions}
                  onSelect={setPrice}
                  ariaLabel="Filter by price. Current: All Prices"
                />
              </div>
              <div className={`relative ${isFiltersVisible ? 'animate-scale-in animation-delay-500' : 'opacity-0'}`}>
                <FilterDropdown
                  title="Sort"
                  currentSelection={sort}
                  options={sortOptions}
                  onSelect={setSort}
                  ariaLabel="Sort products by. Current: New In"
                />
              </div>
            </div>
          </div>

          {/* Floating Decorative Elements for Filters */}
          <div className="absolute -top-2 -right-2 w-12 h-12 bg-brand-accent-teal/10 rounded-full blur-lg animate-parallax-float opacity-40"></div>
          <div className="absolute -bottom-2 -left-2 w-10 h-10 bg-brand-main-red/10 rounded-full blur-md animate-parallax-float animation-delay-300 opacity-30"></div>
        </div>

        {/* Enhanced Product Grid */}
        {loading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-6 gap-y-8 sm:gap-x-8 sm:gap-y-10">
            {[...Array(8)].map((_, index) => (
              <div key={index} className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-white/50 animate-pulse">
                <div className="aspect-[3/4] bg-gray-200 rounded-xl mb-4"></div>
                <div className="p-4">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded mb-3 w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        ) : filteredProducts.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-6 gap-y-8 sm:gap-x-8 sm:gap-y-10">
            {filteredProducts.map((product, index) => {
              const isVisible = visibleProducts.has(index);
              const animationDelay = `animation-delay-${Math.min((index % 8) * 100 + 100, 800)}`;

              return (
                <div
                  key={product.id}
                  ref={(el) => { productRefs.current[index] = el; }}
                  className={`transform transition-all duration-700 ${
                    isVisible
                      ? `animate-fadeIn ${animationDelay} opacity-100 translate-y-0`
                      : 'opacity-0 translate-y-8'
                  }`}
                >
                  {/* Enhanced Product Card Container */}
                  <div className="group relative">
                    {/* Glass Morphism Background */}
                    <div className="absolute inset-0 bg-white/40 backdrop-blur-sm rounded-2xl border border-white/50 shadow-xl opacity-0 group-hover:opacity-100 transition-all duration-500 transform group-hover:scale-105 -m-2 p-2"></div>

                    {/* Product Card */}
                    <div className="relative z-10 transform transition-all duration-500 group-hover:scale-[1.02]">
                      <ProductCard product={product} />
                    </div>

                    {/* Floating Decorative Elements */}
                    <div className="absolute -top-1 -right-1 w-6 h-6 bg-brand-accent-teal/20 rounded-full blur-sm opacity-0 group-hover:opacity-60 transition-opacity duration-500 animate-parallax-float"></div>
                    <div className="absolute -bottom-1 -left-1 w-4 h-4 bg-brand-main-red/20 rounded-full blur-sm opacity-0 group-hover:opacity-40 transition-opacity duration-500 animate-parallax-float animation-delay-300"></div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-16 relative">
            {/* Glass Morphism Background for Empty State */}
            <div className="absolute inset-0 bg-white/30 backdrop-blur-sm rounded-3xl border border-white/30 shadow-xl -m-8 p-8"></div>

            <div className="relative z-10">
              <div className="w-24 h-24 bg-gradient-to-br from-brand-accent-teal/20 to-brand-main-red/20 rounded-full mx-auto mb-6 flex items-center justify-center">
                <div className="w-12 h-12 bg-gradient-to-br from-brand-accent-teal to-brand-main-red rounded-full opacity-60"></div>
              </div>
              <p className="text-2xl text-gray-600 font-semibold mb-3">No products found matching your criteria.</p>
              <p className="text-gray-500 text-lg">Try adjusting your filters to discover more products.</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StorePage;