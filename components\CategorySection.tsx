import React, { useRef, ReactElement, useEffect, useState } from 'react';
import {
    ArrowRightIcon,
    PlayIcon,
    PauseIcon,
    HandWashIcon,
    FabricSoftenerIcon,
    AbayaCareIcon,
    LaundryBubblesIcon,
    DishSparkleIcon,
    ToiletCleanerIcon,
    PowderScoopIcon,
    FreshSparkleIcon,
    EmulsifierDropsIcon
} from './common/Icon';
import { CategoryInfo } from '../types';
import styles from '@/styles/animations.module.css';

// Carousel images from assets/carousal directory
const carouselImages = [
  '/assets/carousal/handwash-1_20250601_19254301.jpg',
  '/assets/carousal/fabric-softner_20250601_19254303.jpg',
  '/assets/carousal/abaya-shampoo_20250601_19254303.jpg',
  '/assets/carousal/laundry-detergent_20250601_19254303.jpg',
  '/assets/carousal/dishwash-liquid_20250601_19254303.jpg',
  '/assets/carousal/toilet-cleaner_20250601_19254303.jpg',
  '/assets/carousal/detergent-powder_20250601_19254303.jpg',
  '/assets/carousal/taze_20250601_19254303.jpg',
  '/assets/carousal/emulsifiers_20250601_19254303.jpg',
];

interface CategoryCardDisplayProps extends CategoryInfo {
  id: string;
  name: string;
  description: string;
  icon: ReactElement;
}

const CategoryCard: React.FC<CategoryCardDisplayProps> = ({
  name,
  description,
  icon
}): ReactElement => {
  return (
    <div
      className="marquee-card relative p-6 rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 ease-out group flex flex-col h-full min-h-[240px] sm:min-h-[260px] overflow-hidden bg-white will-change-transform"
      style={{
        border: '1px solid rgba(255, 255, 255, 0.3)',
      }}
      role="listitem"
      aria-label={`Category: ${name}`}
    >
      {/* Subtle gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-brand-accent-teal/5 via-transparent to-brand-main-red/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

      {/* Glass morphism border effect */}
      <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

      <div className="relative z-10 flex flex-col h-full">
        <div className="mb-3">
          <span className="text-xs font-semibold tracking-widest uppercase text-brand-main-red/80 group-hover:text-brand-main-red transition-colors duration-300">LE PRESTINE</span>
        </div>

        <div
          className="mb-4 w-14 h-14 sm:w-16 sm:h-16 flex items-center justify-center rounded-2xl bg-gradient-to-br from-brand-accent-teal/15 to-brand-accent-teal/25 text-brand-accent-teal transition-all duration-500 ease-out group-hover:from-brand-accent-teal/25 group-hover:to-brand-accent-teal/35 group-hover:scale-105 group-hover:rotate-3 shadow-lg group-hover:shadow-xl will-change-transform"
        >
          {React.cloneElement(icon as React.ReactElement<any>, { size: 28 })}
        </div>

        <h3 className="font-serif text-lg sm:text-xl font-bold mb-2 text-neutral-800 group-hover:text-neutral-900 transition-colors duration-300 leading-tight">{name}</h3>
        <p className="text-sm mb-4 text-gray-600 group-hover:text-gray-700 leading-relaxed flex-grow transition-colors duration-300">{description}</p>

        <button
          className="relative p-3 rounded-2xl transition-all duration-500 ease-out mt-auto self-start focus:outline-none focus:ring-2 focus:ring-offset-2 bg-gradient-to-r from-brand-main-red/10 to-brand-main-red/20 text-brand-main-red hover:from-brand-main-red hover:to-brand-main-red-darker hover:text-white focus:ring-brand-main-red group-hover:scale-105 group-hover:shadow-lg overflow-hidden will-change-transform"
          aria-label={`Explore ${name}`}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          <ArrowRightIcon size={18} strokeWidth={2.5} className="relative z-10 transition-transform duration-300 group-hover:translate-x-1" />
        </button>
      </div>
    </div>
  );
};

const categoriesData: CategoryCardDisplayProps[] = [
  {
    id: 'lp-hand-wash',
    name: 'Hand Wash',
    description: 'Gentle and cleansing for soft, refreshed hands.',
    icon: <HandWashIcon />,
  },
  {
    id: 'lp-fabric-softener',
    name: 'Fabric Softener',
    description: 'Keeps your clothes wonderfully soft and fragrant.',
    icon: <FabricSoftenerIcon />,
  },
  {
    id: 'lp-abaya-shampoo',
    name: 'Abaya Care',
    description: 'Specially formulated for gentle care of abayas.',
    icon: <AbayaCareIcon />,
  },
  {
    id: 'lp-laundry-detergent',
    name: 'Laundry Detergent',
    description: 'Powerful cleaning for all your laundry needs.',
    icon: <LaundryBubblesIcon />,
  },
  {
    id: 'lp-dishwash-liquid',
    name: 'Dishwash Liquid',
    description: 'Cuts through grease for sparkling clean dishes.',
    icon: <DishSparkleIcon />,
  },
  {
    id: 'lp-toilet-cleaner',
    name: 'Toilet Cleaner',
    description: 'Hygienic and effective for a spotless toilet.',
    icon: <ToiletCleanerIcon />,
  },
  {
    id: 'lp-detergent-powder',
    name: 'Detergent Powder',
    description: 'Tough on stains, effective in powder form.',
    icon: <PowderScoopIcon />,
  },
  {
    id: 'lp-taze-detergent-powder',
    name: 'Taze Detergent Powder',
    description: 'Fresh scent with powerful detergent action.',
    icon: <FreshSparkleIcon />,
  },
  {
    id: 'lp-emulsifier',
    name: 'Emulsifier',
    description: 'A versatile emulsifier for various household uses.',
    icon: <EmulsifierDropsIcon />,
  },
];

const CategorySection: React.FC = (): ReactElement => {
  // Use one of our product images as the default thumbnail
  const videoCardImageUrl = "/assets/carousal/handwash-1_20250601_19254301.jpg";

  const [isMarqueeHovering, setIsMarqueeHovering] = React.useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [hasAnimated, setHasAnimated] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  // Carousel state management
  const [isCarouselPlaying, setIsCarouselPlaying] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const carouselIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const itemsPerSetDesktop = 3;
  const originalTotalItems = categoriesData.length;
  const duplicatedCategories = originalTotalItems > 0 ? [...categoriesData, ...categoriesData] : [];

  const animationDuration = React.useMemo(() =>
    Math.max(45, originalTotalItems * 8), [originalTotalItems]
  );

  // Carousel functionality
  const startCarousel = () => {
    setIsCarouselPlaying(true);
    carouselIntervalRef.current = setInterval(() => {
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentImageIndex((prevIndex) =>
          prevIndex === carouselImages.length - 1 ? 0 : prevIndex + 1
        );
        setIsTransitioning(false);
      }, 150);
    }, 2500); // Change image every 2.5 seconds
  };

  const stopCarousel = () => {
    // Add transition effect when stopping
    setIsTransitioning(true);

    // Clear the interval first
    if (carouselIntervalRef.current) {
      clearInterval(carouselIntervalRef.current);
      carouselIntervalRef.current = null;
    }

    // Smooth transition back to thumbnail
    setTimeout(() => {
      setIsCarouselPlaying(false);
      setCurrentImageIndex(0); // Reset to first image
      setTimeout(() => {
        setIsTransitioning(false);
      }, 300); // Allow time for the image change to complete
    }, 150); // Brief pause for smooth transition
  };

  const toggleCarousel = () => {
    if (isCarouselPlaying) {
      stopCarousel();
    } else {
      startCarousel();
    }
  };

  // Cleanup carousel on unmount
  useEffect(() => {
    return () => {
      if (carouselIntervalRef.current) {
        clearInterval(carouselIntervalRef.current);
      }
    };
  }, []);

  // Intersection Observer for scroll-triggered animation
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !hasAnimated) {
            setIsVisible(true);
            setHasAnimated(true);
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, [hasAnimated]);

  return (
    <section
      ref={sectionRef}
      className="relative bg-gradient-to-br from-brand-main-red via-brand-main-red to-brand-main-red-darker overflow-hidden"
      aria-labelledby="category-section-title"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent transform -skew-y-1"></div>
        <div className="absolute inset-0 bg-gradient-to-l from-transparent via-white/3 to-transparent transform skew-y-1"></div>
      </div>

      <div className={`relative container mx-auto px-4 sm:px-6 lg:px-8 pt-12 sm:pt-16 md:pt-20 pb-12 transition-all duration-1000 ease-out ${
        isVisible
          ? 'opacity-100 translate-y-0'
          : 'opacity-0 translate-y-8'
      }`}>
        <h2 id="category-section-title" className="sr-only">Product Categories</h2>

        {/* Section Header */}
        <div className={`text-center mb-12 sm:mb-16 transition-all duration-1000 ease-out ${
          isVisible
            ? 'opacity-100 translate-y-0'
            : 'opacity-0 translate-y-6'
        }`} style={{ transitionDelay: isVisible ? '200ms' : '0ms' }}>
          <h2 className="font-serif text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-4 tracking-tight">
            Our A to Z Solutions
          </h2>
          <p className="text-lg sm:text-xl text-white/90 max-w-2xl mx-auto leading-relaxed">
            From abaya care to everyday essentials, discover our complete range of premium cleaning solutions
          </p>
        </div>

        <div className={`flex flex-col lg:flex-row gap-8 lg:gap-12 lg:items-stretch transition-all duration-1000 ease-out ${
          isVisible
            ? 'opacity-100 translate-y-0'
            : 'opacity-0 translate-y-8'
        }`} style={{ transitionDelay: isVisible ? '400ms' : '0ms' }}>
          {/* Enhanced Carousel Card - MAINTAIN EXACT DIMENSIONS */}
          <div
            className={`w-full lg:w-1/3 relative rounded-3xl overflow-hidden shadow-2xl group flex-shrink-0 min-h-[320px] lg:h-auto backdrop-blur-sm transition-all duration-1000 ease-out ${
              isVisible
                ? 'opacity-100 translate-x-0'
                : 'opacity-0 -translate-x-8'
            }`}
            style={{
              transitionDelay: isVisible ? '600ms' : '0ms',
              aspectRatio: 'auto', // Preserve original aspect ratio
              maxHeight: 'none',
              height: 'auto'
            }}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-brand-accent-teal/20 to-brand-main-red-deepdark/30 z-10"></div>

            {/* Dynamic Image Display - MAINTAIN EXACT ASPECT RATIO */}
            <div className="absolute inset-0 w-full h-full overflow-hidden">
              <img
                src={isCarouselPlaying ? carouselImages[currentImageIndex] : videoCardImageUrl}
                alt={isCarouselPlaying ? `Le Prestine Product ${currentImageIndex + 1}` : "Le Prestine Product Showcase"}
                className={`absolute inset-0 w-full h-full object-cover object-center transition-all duration-500 ease-out group-hover:scale-110 ${
                  isTransitioning ? 'opacity-60 scale-105' : 'opacity-100 scale-100'
                }`}
                style={{
                  minWidth: '100%',
                  minHeight: '100%',
                  maxWidth: 'none',
                  maxHeight: 'none'
                }}
              />

              {/* Carousel Indicators */}
              {isCarouselPlaying && (
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2 z-30">
                  {carouselImages.map((_, index) => (
                    <div
                      key={index}
                      className={`w-2 h-2 rounded-full transition-all duration-300 ${
                        index === currentImageIndex
                          ? 'bg-white shadow-lg'
                          : 'bg-white/40 hover:bg-white/60'
                      }`}
                    />
                  ))}
                </div>
              )}
            </div>

            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent z-20 flex flex-col justify-end p-6 sm:p-8">
              <div className="transform transition-all duration-500 group-hover:translate-y-0 translate-y-2">
                <span className="inline-block px-3 py-1 bg-brand-accent-teal/20 backdrop-blur-sm rounded-full text-xs font-semibold text-white/90 mb-3 border border-white/20">
                  {isCarouselPlaying ? 'PRODUCT SHOWCASE' : 'FEATURED'}
                </span>
                <h3 className="font-serif text-2xl sm:text-3xl font-bold text-white mb-2 leading-tight">
                  {isCarouselPlaying ? 'Premium Product Gallery' : 'Product Showcase'}
                </h3>
                <p className="text-sm sm:text-base text-white/90 mb-4 leading-relaxed">
                  {isCarouselPlaying
                    ? 'Explore our beautiful collection of premium cleaning solutions in action'
                    : 'Discover our premium cleaning solutions and their exceptional quality'
                  }
                </p>
              </div>
            </div>

            <div className="absolute inset-0 flex items-center justify-center z-30">
              <button
                onClick={toggleCarousel}
                className={`relative text-white p-4 sm:p-5 rounded-full transition-all duration-300 ease-out focus:outline-none focus:ring-4 focus:ring-white/30 ${
                  isCarouselPlaying
                    ? 'bg-transparent hover:bg-white/15 hover:backdrop-blur-md hover:shadow-2xl hover:border hover:border-white/20 opacity-0 group-hover:opacity-100 group-hover:scale-110'
                    : 'bg-white/15 backdrop-blur-md hover:bg-white/25 group-hover:scale-110 shadow-2xl border border-white/20'
                }`}
                aria-label={isCarouselPlaying ? "Stop product carousel" : "Start product carousel"}
              >
                <div className="absolute inset-0 rounded-full bg-gradient-to-r from-brand-accent-teal/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                {/* Play Icon - Only visible when carousel is not playing */}
                {!isCarouselPlaying && (
                  <PlayIcon size={28} className="relative z-10 transition-transform duration-300 group-hover:scale-110" />
                )}

                {/* Pause Icon - Only visible on hover when carousel is playing */}
                {isCarouselPlaying && (
                  <PauseIcon
                    size={28}
                    className="relative z-10 transition-all duration-300 ease-out"
                  />
                )}
              </button>
            </div>
          </div>

          {/* Enhanced Category Cards Carousel */}
          <div className={`w-full lg:w-2/3 relative transition-all duration-1000 ease-out ${
            isVisible
              ? 'opacity-100 translate-x-0'
              : 'opacity-0 translate-x-8'
          }`} style={{ transitionDelay: isVisible ? '800ms' : '0ms' }}>
            {/* Desktop Marquee (lg and up) */}
            {originalTotalItems > 0 && (
              <div
                className="hidden lg:block relative h-full"
                onMouseEnter={() => setIsMarqueeHovering(true)}
                onMouseLeave={() => setIsMarqueeHovering(false)}
              >
                <div className="marquee-container relative overflow-hidden rounded-3xl h-full">
                  {/* Dynamic gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-r from-brand-accent-teal/5 via-transparent to-brand-main-red/5 pointer-events-none opacity-60"></div>

                  {/* Advanced shimmer effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-700 pointer-events-none mix-blend-soft-light"></div>

                  <div
                    className={`flex items-stretch h-full ${styles.marquee} ${isMarqueeHovering ? styles.paused : ''}`}
                    style={{
                      width: `${(originalTotalItems * 2 / itemsPerSetDesktop) * 100}%`,
                      animationDuration: `${animationDuration}s`
                    }}
                    role="list"
                  >
                    {duplicatedCategories.map((category, index) => (
                      <div
                        key={`${category.id}-${index}`}
                        className="p-3 flex opacity-100 transform transition-all duration-500 ease-out hover:scale-[1.01] hover:z-10"
                        style={{
                          width: `${100 / (originalTotalItems * 2)}%`,
                          minWidth: '280px'
                        }}
                      >
                        <CategoryCard
                          id={category.id}
                          name={category.name}
                          description={category.description}
                          icon={category.icon}
                        />
                      </div>
                    ))}
                  </div>

                  {/* Elegant pause indicator */}
                  {isMarqueeHovering && (
                    <div className={`absolute top-4 right-4 ${styles.fadeIn} z-20`}>
                      <div className="relative px-4 py-2 rounded-full backdrop-blur-md bg-gradient-to-r from-brand-accent-teal/30 to-brand-main-red/30 border border-white/40 shadow-lg overflow-hidden group">
                        <div className="absolute inset-0 bg-gradient-to-r from-brand-accent-teal/10 to-brand-main-red/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        <div className="flex items-center gap-2 relative">
                          <div className="w-2 h-2 bg-white rounded-full animate-[pulse_2s_infinite]"></div>
                          <span className="text-xs font-semibold text-white tracking-wider">Paused</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Tablet Grid (md to lg) */}
            <div className="hidden md:block lg:hidden">
              <div className="grid grid-cols-2 gap-4" role="list">
                {categoriesData.slice(0, 6).map((category) => (
                  <CategoryCard
                    key={category.id}
                    id={category.id}
                    name={category.name}
                    description={category.description}
                    icon={category.icon}
                  />
                ))}
              </div>
            </div>

            {/* Mobile Horizontal Scroller (sm and down) */}
            <div className="md:hidden">
              <div className="flex overflow-x-auto space-x-4 py-2 -mx-2 px-2 scrollbar-hide" role="list">
                {categoriesData.map((category) => (
                  <div key={category.id} className="w-64 sm:w-72 flex-shrink-0">
                    <CategoryCard
                      id={category.id}
                      name={category.name}
                      description={category.description}
                      icon={category.icon}
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced bottom transition with glass morphism */}
      <div className="relative w-full h-8 -mt-8 z-10">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent to-brand-accent-teal/20"></div>
        <div className="bg-brand-accent-teal w-full h-full rounded-t-3xl shadow-2xl backdrop-blur-sm border-t border-white/20"></div>
      </div>
    </section>
  );
};

export default CategorySection;
